"server-only"

import { EnrichedWishlistItem } from "@/types/wishlist";
import WishlistProduct from "./WishlistProduct";
import Link from "next/link";

export default function WishlistPage({
  wishlistItems,
}: {
  wishlistItems: EnrichedWishlistItem[];
}) {
  return (
    <div className="max-w-[1640px] mx-auto p-4">
      <h1 className="text-2xl font-semibold mb-6">
        Favorite ({wishlistItems.length}{" "}
        {wishlistItems.length === 1 ? "produs" : "produse"})
      </h1>
      <div className="border rounded-lg overflow-hidden shadow">
        {wishlistItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 bg-gradient-to-br from-purple-50 to-indigo-100 rounded-xl shadow-lg text-center transform transition-all duration-300 hover:scale-105">
            <div className="mb-6">
              {/* You can replace this with a more elaborate SVG icon or illustration */}
              <svg
                className="w-24 h-24 text-indigo-400 opacity-80 animate-bounce-slow"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                ></path>
              </svg>
            </div>

            <h2 className="text-3xl md:text-4xl font-extrabold text-gray-800 mb-4 leading-tight">
              Your Wishlist is a Blank Canvas!
            </h2>
            <p className="text-lg text-gray-600 mb-6 max-w-prose">
              It looks like you haven't added any dream items yet. This is the perfect
              opportunity to discover something you'll truly love.
            </p>

            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link href="/products">
                <p className="px-8 py-3 bg-indigo-600 text-white font-semibold rounded-full shadow-lg hover:bg-indigo-700 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                  Start Exploring Products
                </p>
              </Link>
              <Link href="/categories">
                <p className="px-8 py-3 bg-white text-indigo-700 font-semibold rounded-full border border-indigo-300 shadow-lg hover:bg-gray-50 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-indigo-300">
                  Browse Our Categories
                </p>
              </Link>
            </div>

            <p className="text-sm text-gray-500 mt-8">
              Found something you love? Just click the heart icon to add it here!
            </p>
          </div>
        ) :  wishlistItems.map((item) => (
              <WishlistProduct key={item.product.Material_Number} item={item} />
            ))}
      </div>
    </div>
  );
}

//initial
// export default async function WishlistPage({wishlistItems}: {wishlistItems: WishlistItems[]}){
  
//   return (
//       <div className="mb-16">
//         <h1 className="text-2xl font-semibold my-4">
//          Favorite ({wishlistItems.length} {wishlistItems.length === 1 ? "produs" : "produse"})
//         </h1>

//         <div className="border dark:border-gray-700 rounded-lg shadow">
//           { wishlistItems.map((item, key) => (
//             <WishlistProduct item={item} key={key} />
//           ))}
//         </div>
//       </div>
//   );
// };
