// lib/db.ts
import sql from "mssql";
import { logger } from "@/lib/logger";

async function withRetryMSSQL<T>(
  fn: () => Promise<T>,
  { retries = 2, delay = 1000, backoff = 1.5 }: { retries?: number; delay?: number; backoff?: number } = {}
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) throw error;

    const isRetryableError = (error: unknown): boolean => {
      if (!(error instanceof Error)) return false;
      
      const message = error.message.toLowerCase();
      
      // Connection-related errors
      const connectionErrors = [
        'connection pool',
        'connection timeout',
        'econnrefused',
        'econnreset',
        'etimedout',
        'etimeout', 
        'socket hang up',
        'connection lost',
        'connection terminated unexpectedly',
        'failed to connect',
        'login timeout expired',
        'connection is closed',
        'requesterror: connection is closed',
        'transactionerror: connection is closed',
        'connectionerror',
      ];

      // Deadlock and transient SQL errors
      const transientErrors = [
        'deadlock victim',
        'was deadlocked',
        'transaction (process id',
        'timeout expired',
        'lock request time out',
        'could not continue scan',
        // Add other MSSQL transient errors
        'transport-level error',
        'communication link failure',
      ];

      // Check error number for MSSQL (if available)
      const mssqlError = error as any;
      if (mssqlError.number) {
        const retryableErrorNumbers = [
          1205, // Deadlock victim
          1222, // Lock request time out period exceeded
          8645, // A timeout occurred while waiting for memory resources
          8628, // A time out occurred while waiting to optimize the query
        ];
        
        if (retryableErrorNumbers.includes(mssqlError.number)) {
          return true;
        }
      }

      return [...connectionErrors, ...transientErrors].some(errorText => 
        message.includes(errorText)
      );
    };

    if (!isRetryableError(error as unknown)) throw error;

    // Different delay strategy for deadlocks vs connection errors
    const isDealock = (error instanceof Error && error.message.toLowerCase().includes('deadlock')) ||
                     (error as any).number === 1205;
    
    const retryDelay = isDealock 
      ? Math.random() * 100 + 50  // Random 50-150ms for deadlocks
      : delay;                    // Regular backoff for connection errors

    logger.warn(`MSSQL operation failed, retrying (${retries} attempts left)...`, {
      error: error instanceof Error ? error.message : String(error),
      errorNumber: (error as any).number,
      retryDelay
    });

    await new Promise(resolve => setTimeout(resolve, retryDelay));

    return withRetryMSSQL(fn, {
      retries: retries - 1,
      delay: isDealock ? retryDelay : Math.min(delay * backoff, 3000),
      backoff
    });
  }
}

// 1. Load and validate env
const {
  DB_USER,
  DB_PASSWORD,
  DB_SERVER,
  DB_NAME,
  DB_ENCRYPT = "true",
  DB_TRUST_CERT = "true",
} = process.env;

if (!DB_USER || !DB_PASSWORD || !DB_SERVER || !DB_NAME) {
  throw new Error(
    "[mssql] Missing one of DB_USER, DB_PASSWORD, DB_SERVER, or DB_NAME in env"
  );
}

// 2. Pool config
const poolConfig: sql.config = {
  user: DB_USER,
  password: DB_PASSWORD,
  server: DB_SERVER,
  database: DB_NAME,
  options: {
    encrypt: DB_ENCRYPT === "true",
    trustServerCertificate: DB_TRUST_CERT === "true",
    enableArithAbort: true,
  },
  pool: {
    max: 3,
    min: 0,
    idleTimeoutMillis: 15000,  // Shorter timeout
    acquireTimeoutMillis: 15000,  // Shorter timeout
  },
};

// 3. Singleton pool promise
let poolPromise: Promise<sql.ConnectionPool> | null = null;

function getPool(): Promise<sql.ConnectionPool> {
  if (!poolPromise) {
    poolPromise = sql.connect(poolConfig)
      .then((pool) => {
        // Optional: hook into process exit to close pool
        if (process.env.NODE_ENV === 'development') {
          process.on("SIGINT", async () => {
            await pool.close();
            process.exit(0);
            },
          );
          process.on("SIGTERM", async () => {
            await pool.close();
            process.exit(0);
            },
          );
          process.on("beforeExit", async () => {
            await pool.close();
            process.exit(0);
            },
          );
        };
        return pool;
      })
      .catch((err) => {
        poolPromise = null;
        logger.error("[db] MSSQL connection failed", err);
        throw err;
      });
  }
  return poolPromise;
}

// 4. Helper to bind types
function bindRequest(
  request: sql.Request,
  params: Record<string, unknown>
) {
  for (const [name, val] of Object.entries(params)) {
    if (val == null) {
      request.input(name, sql.NVarChar(sql.MAX), null);
    } else if (typeof val === "number") {
      request.input(name, sql.Float, val);
    } else if (typeof val === "boolean") {
      request.input(name, sql.Bit, val);
    } else if (val instanceof Date) {
      request.input(name, sql.DateTime, val);
    } else {
      // default to NVARCHAR(MAX) for strings and anything else
      request.input(name, sql.NVarChar(sql.MAX), String(val));
    }
  }
}

// 5. DB class
class Database {
  /** Run a SELECT/INSERT/UPDATE/... with parameters */
  async query<T>(
    queryText: string,
    params: Record<string, unknown> = {}
  ): Promise<T[]> {
      return withRetry(async () => {
        const pool = await getPool();
        const req = pool.request();
        bindRequest(req, params);

        try {
          const result = await req.query<T>(queryText);
          return result.recordset;
        } catch (err) {
          logger.error("[mssql] Query failed", { queryText, params, err });
          throw err;
        }
        }
      );
  }

  /** Execute a stored procedure with inputs + single numeric output */
  async executeProcedure<T = number>(
    procName: string,
    inputs: Record<string, unknown>,
    outputName: string
  ): Promise<T> {
    return withRetry(async () => {
      const pool = await getPool();
      const req = pool.request();
      bindRequest(req, inputs);
      req.output(outputName, sql.Float);

      try {
        const result = await req.execute(procName);
        return result.output[outputName] as T;
      } catch (err) {
        logger.error("[mssql] Procedure failed", { procName, inputs, err });
        throw err;
      }
    });
  }

  /** Quick health check */
  async healthCheck(): Promise<boolean> {
    try {
      const pool = await getPool();
      await pool.request().query("SELECT 1");
      return true;
    } catch {
      return false;
    }
  }
}

export const mssql = new Database();





//initial 
// import sql from 'mssql';
// import { RateLimiter } from './rateLimiter';
// import { logger } from '../logger';

// const config = {
//   user: process.env.DB_USER,
//   password: process.env.DB_PASSWORD,
//   server: process.env.DB_SERVER!,
//   database: process.env.DB_NAME,
//   options: {
//     encrypt: true,
//     trustServerCertificate: true,
//     enableArithAbort: true
//   },
//   pool: {
//     max: 10,
//     min: 0,
//     idleTimeoutMillis: 30000,
//     acquireTimeoutMillis: 30000
//   }
// };

// class Database {
//   private static instance: Database;
//   private pool!: sql.ConnectionPool;
//   private rateLimiter = new RateLimiter(30000, 3600); // 100 requests/hour

//   private constructor() {}

//   public static getInstance(): Database {
//     if (!Database.instance) {
//       logger.info('[MSSQL.ts] Creating new database instance for MSSQL');
//       Database.instance = new Database();
//     }
//     return Database.instance;
//   }

//   public async connect(): Promise<sql.ConnectionPool> {
//     if (!this.pool) {
//       this.pool = new sql.ConnectionPool(config);
//       await this.pool.connect();
//       logger.info('[MSSQL.ts] Database pool created for MSSQL');
//     } else if (!this.pool.connected) {
//       logger.info('[MSSQL.ts] Reconnecting to database for MSSQL');
//       await this.pool.connect();
//     }
//     logger.info('[MSSQL.ts] Database pool connected for MSSQL');
//     return this.pool;
//   }

//   public async disconnect(): Promise<void> {
//     if (this.pool) {
//       await this.pool.close();
//       logger.info('[MSSQL.ts] Database pool closed for MSSQL');
//     }
//   }

//   public async queryWithParams<T>(query: string, params: { [key: string]: any }): Promise<sql.IRecordSet<T>> {

//     // const clientIp = getClientIp();

//     // if (clientIp !== null) {
//     //   if (!this.rateLimiter.checkLimit(clientIp)) {
//     //     throw new Error('Rate limit exceeded');
//     //   }
//     // } else {
//     //   throw new Error('Unable to get client IP');
//     // }

//     const pool = await this.connect();
//     const ps = new sql.PreparedStatement(pool);
    
//     try {
//       // Add parameters dynamically
//       Object.entries(params).forEach(([name, value]) => {
//         const type = typeof value === 'number' ? sql.Int : sql.VarChar;
//         ps.input(name, type);
//       });

//       await ps.prepare(query);
//       const result = await ps.execute(params);
//       await ps.unprepare();
      
//       //logger.info('Query executed', { query, params });
//       //logger.info('Query executed', { params });
//       return result.recordset as sql.IRecordSet<T>;
//     } catch (error) {
//       logger.error('[MSSQL.ts] Query execution failed', { error, query, params });
//       throw error;
//     } finally {
//       await ps.unprepare().catch(() => {});
//     }
//   }

//   public async procedureFourLevelDiscountPerProduct(procedureName: string, params: { [key: string]: any }, outputParam: string): Promise<number> {

//     // const clientIp = getClientIp();
//     // if (clientIp !== null) {
//     //   if (!this.rateLimiter.checkLimit(clientIp)) {
//     //     throw new Error('Rate limit exceeded');
//     //   }
//     // } else {
//     //   throw new Error('Unable to get client IP');
//     // }

//     const pool = await this.connect();
//     const request = pool.request();

//     try {
//         // Add input parameters dynamically
//         Object.entries(params).forEach(([name, value]) => {
//             const type = typeof value === 'number' ? sql.Int : sql.VarChar;
//             request.input(name, type, value);
//         });

//         // Add the output parameter
//         request.output(outputParam, sql.Float);

//         // Execute the stored procedure
//         const result = await request.execute(procedureName);

//         // Retrieve and return the output parameter
//         return result.output[outputParam] as number;
//     } catch (error) {
//         logger.error('Stored procedure execution failed', { error, procedureName, params });
//         throw error;
//     }
// }


//   public async healthCheck() {
//     try {
//       const pool = await this.connect();
//       await pool.request().query('SELECT 1');
//       return true;
//     } catch (error) {
//       return false;
//     }
//   }
// }

// export const mssql = Database.getInstance();