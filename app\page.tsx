
import { getCurrentDbUser } from "@/lib/auth";
import ProductGrid from "./components/product/ProductGrid";
import { getFeaturedProducts } from "./getData/products";
import CategoryGrid from "./components/categories/CategoryGridLandingPage";
import HeroSection from "./components/banner/HeroSection";
import { getCategorySectionLandingPageBanners, getHeroBanners } from "./getData/banners";
import { getWishlistProductCodes } from "./getData/wishlist";
import { redirect } from "next/navigation";
import { LandingPageProduct } from "@/types/product";

import { getPretSiStocBatch } from "@/lib/mssql/query";

export default async function Home() {

   const user = await getCurrentDbUser();

   if(!user){
    return redirect("sign-in")
   }

  //  const ordered = user ? await getRecentOrders(user.id) : [];

  //  const featured: LandingPageProduct[] = await getFeaturedProducts()
   

  // // Step 1: Get wishlist product codes
  // const wishlistProductCodes = user
  //   ? await getWishlistProductCodes(user.id)
  //   : new Set<string>();

  // // Step 2: Get stock info in parallel
  // const stockStatuses = await Promise.all(
  //   featured.map((p) => getStockForProduct(p.Material_Number))
  // );

  // const productsWithData = featured.map((product, index) => ({
  //   ...product,
  //   isInWishlist: wishlistProductCodes.has(product.Material_Number),
  //   stock: stockStatuses[index], // stock is 0 or 1
  // }));

  // 1) First, grab your featured products
  // const featured: LandingPageProduct[] = await getFeaturedProducts()

  // // 2) In one go, fetch both the user’s wishlist and the batch of price+stock data
  // const [wishlistProductCodes, pretSiStocMap] = await Promise.all([
  //   user
  //     ? getWishlistProductCodes(user.id)
  //     : Promise.resolve(new Set<string>()),
  //   getPretSiStocBatch(featured.map((p) => p.Material_Number)),
  // ])

  // // 3) Merge everything back onto your featured list
  // const productsWithData = featured.map((product) => {
  //   // find the array of PretSiStocAM entries for this product
  //   const batchEntries = pretSiStocMap[product.Material_Number] ?? []

  //   // decide how you want to collapse them: e.g. sum all locations’ stock
  //   const totalStock = batchEntries.reduce((sum, e) => sum + e.stoc, 0)

  //   // maybe use the first entry’s pret as the displayed price, or ignore it
  //   const displayPrice = batchEntries[0]?.pret ?? product.FinalPrice

  //   return {
  //     ...product,
  //     isInWishlist: wishlistProductCodes.has(product.Material_Number),
  //     stock: totalStock,
  //     displayPrice,
  //   }
  // })

  //  const heroBanners = await getHeroBanners()

  //  const categories = await getCategorySectionLandingPageBanners()

  return (
    <>HOME PAGE
      {/* <HeroSection heroBanners={heroBanners} />

      <CategoryGrid  categories={categories} /> */}

      {/* {ordered.length > 0 && <ProductGrid products={ordered} title="Produse recente" description="Produse pe care le-ai cumparat recent" />  } */}
{/* 
      {featured.length > 0 && <ProductGrid stock={0} products={productsWithData} title="Produse recomandate" description="Produse care te-ar putea interesa" /> }  */}

    </>
  );
}


