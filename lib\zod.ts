import { z } from 'zod'; // For input validation

export const FamilyCodeSchema = z.object({
    familyCode: z.
        string().
        min(4, { message: 'Family code must be at least 4 characters long' }).
        max(10, { message: 'Family code must be at most 10 characters long' })
});


//export const userIdStringSchema = z.string().min(24);
export const productCodSchema = z.string().length(11)
export const cuidSchema =   z.string().cuid()
export const userIdClerkSchema =  z.string().startsWith('user_')

export const updateCartSchema = z.object({
    itemId: cuidSchema,
    vinNotes: z.string().optional(),
    addVinNotesToInvoice: z.boolean().optional(),
    addToOrder: z.boolean().optional(),
    quantity: z.number().optional(),
});


export const categoryIdSchemaMSSQL = z.string().min(7).max(20) // Alphanumeric with dashes only
//export const categoryIdSchemaMSSQL = z.string().min(1).max(20).regex(/^[A-Z0-9-]+$/i) // Alphanumeric with dashes only

