import type { DiscountType as PrismaDiscountType } from "@/generated/prisma";
import type { StockStatus as PrismaStockStatus } from "@/generated/prisma";

export interface LandingPageProduct {
  stock?: number | null;
  displayPrice?: number | null;
  id: string;
  Material_Number: string;
  Description_Local: string | null;
  PretAM: number | null;
  FinalPrice: number | null;
  ImageUrl: string[];
  categoryLevel3: { name: string } | null;
  HasDiscount: boolean;
  discountPercentage: number | null;
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
  productClass: {
    vehicleModels: {
      vehicleModel: { name: string };
    }[];
  } | null;
}

export interface ProductPage {
    id: string;
    Material_Number: string;
    Net_Weight: string | null;
    Description_Local: string | null;
    Base_Unit_Of_Measur: string | null;
    Cross_Plant: string | null;
    New_Material: string | null;
    PretAM: number | null;
    FinalPrice: number | null;
    HasDiscount: boolean;
    activeDiscountType: PrismaDiscountType | null;
    activeDiscountValue: number | null;
    discountPercentage: number | null;
    ImageUrl: string[];
    attributes: {
        key: string | null;
        value: string | null;
    }[];
    productClass: {
        vehicleModels: {
            vehicleModel: {
                name: string;
            };
        }[];
    } | null;
    categoryLevel3: { name: string } | null;
    classCode: string;
    brandName: string;
}

// export interface RecentOrderItem {
//   id: string;
//   Material_Number: string;
//   Description_Local: string | null;
//   PretAM: number | null;
//   FinalPrice: number | null;
//   priceRange: string | null;
//   ImageUrl: string[];
//   categoryLevel3: string | null;          // name
//   HasDiscount: boolean;
//   activeDiscountType: PrismaDiscountType | null;
//   activeDiscountValue: number | null;
//   discountPercentage: number | null;
//   vehicleModels: string[];                // array of model names
//   stockStatus: PrismaStockStatus;
//   isInWishlist: boolean;
// }

// export interface RecentOrder {
//   id: string;
//   createdAt: Date;
//   items: RecentOrderItem[];
// }

// export interface ProductRouteProps{
//     Material_Number: string;
//     Net_Weight: string | null;
//     Description_Local: string | null;
//     Base_Unit_Of_Measur: string | null;
//     Cross_Plant: string | null;
//     New_Material: string | null;
//     PretAM: number | null; //Decimal | null;
//     FinalPrice: number | null; //Decimal | null;
//     HasDiscount: boolean;
//     activeDiscountType: ActiveDiscountType;
//     activeDiscountValue: number | null;
//     discountPercentage: number | null; //Decimal | null;
//     ImageUrl: string[];
//     attributes: {
//         key: string | null;
//         value: string | null;
//     }[] | null;
//     productClass: {
//         vehicleModels: {
//             vehicleModel: {
//                 name: string;
//             };
//         }[];
//     } | null;
//     categoryLevel3: {
//         name: string | null;
//     } | null;
// }