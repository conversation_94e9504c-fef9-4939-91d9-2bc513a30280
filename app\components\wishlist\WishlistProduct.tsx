
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingCart, Trash } from "lucide-react";
import { cn, formatDiscount, formatPriceRON, getStockStatus } from "@/lib/utils";
import RemoveProductWishlist from "./WishlistRemoveButton";
import { WishlistItems, EnrichedWishlistItem  } from "@/types/wishlist";
import CartButtonWishlist from "../cart/CartButtonWishlist";
import { Badge } from "@/components/ui/badge";


export default function WishlistProduct({
  item,
}: {
  item: EnrichedWishlistItem;
}) {
  const { product, stock, displayPrice } = item;
  const stockStatus = getStockStatus(stock);

  return (
    <div className="flex items-center gap-6 p-6 border-b last:border-0">
      {/* Image */}
      <div className="w-24 h-24 rounded-lg overflow-hidden bg-input">
        <img
          src={product.ImageUrl[0]}
          alt={product.Description_Local ?? ""}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Details */}
      <div className="flex-1">
        <h3 className="text-lg font-medium text-foreground">
          {product.Description_Local}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          OE Code: {product.Material_Number}
        </p>
        <Badge
          className={cn(
            "mt-2 text-xs",
            stock > 10
              ? "bg-green-100 text-green-800"
              : stock > 0
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          )}
        >
          {stockStatus.replace(/_/g, " ")}
        </Badge>
      </div>

      {/* Pricing & Actions */}
      <div className="flex flex-col items-end space-y-2 text-right">
        {/* Old Price + Discount */}
        {product.HasDiscount && (
          <div className="flex items-baseline space-x-2">
            <span className="text-sm text-muted-foreground line-through">
              {formatPriceRON(product.PretAM)}
            </span>
            <Badge variant="destructive" className="text-xs">
              {formatDiscount(
                product.activeDiscountType,
                product.activeDiscountValue
              )}
            </Badge>
          </div>
        )}

        {/* Display Price */}
        <span className="text-lg font-semibold text-primary">
          {formatPriceRON(displayPrice)}
        </span>

        {/* Wishlist Actions */}
        <div className="flex space-x-2">
          <CartButtonWishlist product={product.Material_Number} />
          <RemoveProductWishlist product={product.Material_Number} />
        </div>
      </div>
    </div>
  );
}


//initial
// export default async function WishlistProduct({item}: {item: WishlistItems}){

//     const stockStatus = getStockStatus(1); //utility that formats the stock status but it needs to be changed to get the stock from the external database
//     return(
//         <>
//         <div
//               key={item.product.Material_Number}
//               className="flex items-center gap-6 p-6 border-b border-gray-200 last:border-0"
//             >
//               <div className="w-24 h-24 rounded-lg overflow-hidden">
//                 <img
//                   src={item.product.ImageUrl[0]}
//                   alt={item.product.Description_Local || ""}
//                   className="w-full h-full object-cover"
//                 />
//               </div>

//               <div className="flex-1">
//                 <h3 className="text-lg font-medium">{item.product.Description_Local || ""}</h3>
//                 <p className="text-sm text-gray-500 mt-1">
//                   OE Code: {item.product.Material_Number}
//                 </p>
//                 <p className="text-sm mt-1">
//                     <span
//                       className={cn(
//                         "px-2 py-1 rounded-full text-xs",
//                         stockStatus === "in stoc"
//                           ? "bg-green-100 text-green-800"
//                           : stockStatus === "stoc mic"
//                             ? "bg-yellow-100 text-yellow-800"
//                             : "bg-red-100 text-red-800",
//                       )}
//                     >
//                       {stockStatus.replace(/_/g, " ")}
//                     </span>  
//                 </p> 
//               </div>

//               <div className="flex flex-col items-end space-y-2 text-right">
//                 {/* Old Price + Discount Badge */}
//                 {item.product.HasDiscount && (
//                   <div className="flex items-baseline space-x-2">
//                     <span className="text-sm text-muted-foreground line-through">
//                       {formatPriceRON(item.product.PretAM)}
//                     </span>
//                     <Badge variant="destructive" className="text-xs">
//                       {formatDiscount(
//                         item.product.activeDiscountType,
//                         item.product.activeDiscountValue
//                       )}
//                     </Badge>
//                   </div>
//                 )}

//                 {/* Final Price */}
//                 <span className="text-lg font-semibold text-primary">
//                   {formatPriceRON(item.product.FinalPrice)}
//                 </span>

//                 {/* Actions */}
//                 <div className="flex space-x-2">
//                   <CartButtonWishlist product={item.product.Material_Number} />
//                   <RemoveProductWishlist product={item.product.Material_Number} />
//                 </div>
//               </div>
//             </div>
//         </>
//     )
// }