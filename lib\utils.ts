"server-only"

import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getClientIp(req: Request): string | null {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) return forwarded.split(',')[0].trim();
  return req.headers.get('x-real-ip');
}

export  function getStockStatus(stock?: number) {
      if (stock === undefined) return "UNKNOWN";
      if (stock > 5) return "in stoc";
      if (stock > 0) return "stoc mic";
      return "stoc epuizat";
}

export function formatPriceRON(price: number | null): string {
  if (typeof price !== "number") return "";

  const formatted = new Intl.NumberFormat("ro-RO", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);

  return `${formatted} Lei`; // Capitalized
}

export function formatDiscount(
  type: "PERCENTAGE" | "FIXED_AMOUNT" | "NEW_PRICE" | null,
  value: number | null
): string | null {
  if (type === null || value === null) return null;

  const formatted = value.toLocaleString("ro-RO", { minimumFractionDigits: 2 });

  switch (type) {
    case "PERCENTAGE":
      return `-${formatted}%`;
    case "FIXED_AMOUNT":
      return `-${formatted} RON`;
    case "NEW_PRICE":
      return `${formatted} RON`;
    default:
      return null;
  }
}

export function toSafeNumber(
  x: unknown
): number | null {
  if (x == null) return null;
  // Prisma Decimal instance
  if (typeof x === "object" && typeof (x as any).toNumber === "function") {
    return (x as any).toNumber();
  }
  // Already a string or number
  const n = Number(x);
  return Number.isNaN(n) ? null : n;
}

export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const UNIT_TRANSLATIONS: Record<string,string> = {
  PCE:   "Bucata",
  PK:    "Pachet",
  MTR:   "Metru",
  PA:    "Set",
  LTR:   "Litru",
  GRM:   "Gram",
  CM:    "Centimetru",
  SQM:   "Metru Patrat",
  SHEET: "Foaie",
};

export function translateUnit(unit: string): string {
  return UNIT_TRANSLATIONS[unit] ?? unit;
}