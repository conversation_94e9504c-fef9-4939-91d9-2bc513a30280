"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ArrowLeft, Home, Search, Wrench } from "lucide-react";

export default function NotFoundPage() {
const navigate = useRouter().push;
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:bg-black flex items-center  justify-center px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* BMW Logo Area */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-[#0066B1] rounded-full mb-6 animate-pulse-slow">
            <Wrench className="w-10 h-10 text-white" />
          </div>
        </div>

        {/* 404 Number */}
        <div className="mb-8">
          <h1 className="text-9xl md:text-[12rem] font-bold text-transparent bg-gradient-to-r from-[#0066B1] to-[#4D4D4D] bg-clip-text leading-none animate-fade-in">
            404
          </h1>
        </div>

        {/* Error Message */}
        <div className="mb-12 space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-[#4D4D4D] mb-4">
            Part Not Found
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
            The BMW part you're looking for seems to have taken a detour. Don't
            worry – our premium collection of authentic BMW parts is just a
            click away.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            

          <Button
            onClick={() => navigate("/")}
            className="bg-[#0066B1] hover:bg-[#0052A3] text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Home className="w-5 h-5 mr-2" />
            Return Home
          </Button>

          <Button
            onClick={() => navigate("/search")}
            variant="outline"
            className="border-[#0066B1] text-[#0066B1] hover:bg-[#0066B1] hover:text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Search className="w-5 h-5 mr-2" />
            Search Parts
          </Button>

          <Button
            onClick={() => history.back()}
            variant="ghost"
            className="text-[#4D4D4D] hover:text-[#0066B1] px-8 py-3 text-lg font-semibold transition-all duration-300"
            size="lg"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go Back
          </Button>
        </div>

        {/* Popular Categories */}
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto">
          <h3 className="text-xl font-bold text-[#4D4D4D] mb-6">
            Popular BMW Parts Categories
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { name: "Engine Parts", count: "2,847" },
              { name: "Brake System", count: "1,923" },
              { name: "Suspension", count: "1,456" },
              { name: "Electrical", count: "3,201" },
              { name: "Body Parts", count: "2,134" },
              { name: "Interior", count: "1,789" },
              { name: "Exhaust", count: "892" },
              { name: "Filters", count: "1,567" },
            ].map((category, index) => (
              <button
                key={index}
                onClick={() =>
                  navigate(
                    `/search?category=${category.name.toLowerCase().replace(" ", "-")}`,
                  )
                }
                className="p-4 rounded-xl border border-slate-200 hover:border-[#0066B1] hover:bg-[#0066B1]/5 transition-all duration-300 group text-left"
              >
                <div className="font-semibold text-[#4D4D4D] group-hover:text-[#0066B1] transition-colors">
                  {category.name}
                </div>
                <div className="text-sm text-slate-500 mt-1">
                  {category.count} parts
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Premium Badge */}
        <div className="mt-12">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#0066B1] to-[#4D4D4D] rounded-full text-white font-semibold shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
            Genuine BMW Parts • Premium Quality • Worldwide Shipping
          </div>
        </div>
      </div>
    </div>
  );
}
